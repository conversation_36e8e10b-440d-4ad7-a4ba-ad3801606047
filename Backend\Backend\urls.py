from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from rest_framework import permissions, views
from rest_framework.response import Response
from drf_yasg.views import get_schema_view
from drf_yasg import openapi
from core.views import AuthViewSet
from django.shortcuts import render
from core.views import index

schema_view = get_schema_view(
    openapi.Info(
        title="ShuleXcel API",
        default_version='v1',
        description="""
        # ShuleXcel School Management System API Documentation

        Welcome to the ShuleXcel API documentation. This API provides a comprehensive set of endpoints
        for managing various aspects of a school, including students, teachers, staff, academic records,
        fees, inventory, library resources, communication, and more.

        ## Key Features:

        *   **School & Branch Management:** Endpoints for managing school details, branches, and configurations.
        *   **User Management:** Manage users, roles, and permissions within the system.
        *   **Authentication & Authorization:** Secure access to API endpoints using token-based authentication.
        *   **Academic Management:** Handle academic years, classes, subjects, and student enrollment.
        *   **Curriculum & Syllabus:** Manage curriculum systems, education levels, syllabi, and units.
        *   **Fee Management:** Control fee structures, payments, and financial records.
        *   **Inventory Management:** Track school assets and supplies.
        *   **Library Management:** Manage library books, resources, and borrowings.
        *   **Communication:** Utilize endpoints for announcements, events, and notifications.
        *   **System Settings:** Configure various system-wide settings.
        *   **Fleet Management:** Manage school vehicles and transportation routes.
        *   **Community Features:** Facilitate interaction through communities, posts, and comments.
        *   **Analytics & Reporting:** Access data for insights and reporting.

        ## Authentication

        This API uses **Bearer Token** authentication. Include your token in the `Authorization` header
        as `Bearer <your_token>` for authenticated requests.

        ---

        **Note:** This documentation is generated using `drf-yasg`.
        """,
        terms_of_service="https://www.shulexcel.com/terms/",
        contact=openapi.Contact(email="<EMAIL>"),
        license=openapi.License(name="Proprietary License"),
    ),
    public=True,
    permission_classes=(permissions.AllowAny,),
    patterns=[
        path('', index, name='index'),
        # Include all your API URLs here
        path('api/', include([
            path('schools/', include('schools.urls')),
            path('users/', include('users.urls')),
            path('core/', include('core.urls')),
            path('academics/', include('academics.urls')),
            path('academics/syllabus/', include('syllabus.urls')),
            path('analytics/', include(('academics.api.urls', 'analytics'))),
            path('fees/', include('fees.urls')),
            path('library/', include('library.urls')),
            path('inventory/', include('inventory.urls')),
            path('communication/', include('communication.urls')),
            path('settings/', include('settings_app.urls')),
            path('fleet/', include('fleet.urls')),
            path('community/', include('community.urls')),
            path('statistics/', include('school_statistics.urls')),
        ])),
    ],
)

class APIRootView(views.APIView):
    """
    Root view for the API that lists all available endpoints
    """
    permission_classes = [permissions.AllowAny]

    def get(self, request):
        return Response({
            # School Management
            'schools': {
                'description': 'School management endpoints',
                'schools_list': request.build_absolute_uri('/api/schools/schools/'),
                'branches_list': request.build_absolute_uri('/api/schools/branch/'),
            },
            # Other modules
            'fees': request.build_absolute_uri('/api/fees/'),
            'core': request.build_absolute_uri('/api/core/'),
            'users': request.build_absolute_uri('/api/users/'),
            'academics': request.build_absolute_uri('/api/academics/'),
            'analytics': request.build_absolute_uri('/api/analytics/'),
            'dashboard': request.build_absolute_uri('/api/dashboard/metrics/'),
            # New API endpoints
            'library': request.build_absolute_uri('/api/library/'),
            'inventory': request.build_absolute_uri('/api/inventory/'),
            'communication': request.build_absolute_uri('/api/communication/'),
            'settings': request.build_absolute_uri('/api/settings/'),
            'fleet': request.build_absolute_uri('/api/fleet/'),
            'community': request.build_absolute_uri('/api/community/'),
            'statistics': request.build_absolute_uri('/api/statistics/'),
            'mpesa_integration': request.build_absolute_uri('/api/mpesa/'),
            'syllabus': request.build_absolute_uri('/api/academics/syllabus/'),
            'documentation': {
                'swagger': request.build_absolute_uri('/swagger/'),
                'redoc': request.build_absolute_uri('/redoc/'),
            }
        })


urlpatterns = [
    path('', index, name='index'),  # Add index page
    path('admin/', admin.site.urls),
    path('admin-panel/', include('ShuleXcel.admin_urls')),  # Admin panel URLs
    path('api/', APIRootView.as_view(), name='api-root'),# Schools and branches are now consistently under the schools app
    path('api/schools/', include('schools.urls')),    # Fees module URLs
    path('api/fees/', include('fees.urls')),
    path('api/core/', include('core.urls')),
    path('api/users/', include('users.urls')),
    # Add direct auth endpoints for compatibility with frontend
    path('api/auth/login/', AuthViewSet.as_view({'post': 'login'}), name='auth_login'),
    path('api/auth/logout/', AuthViewSet.as_view({'post': 'logout'}), name='auth_logout'),
    path('api/auth/me/', AuthViewSet.as_view({'get': 'me'}), name='auth_me'),
    # Academic module URLs
    path('api/academics/', include('academics.urls')),
    path('api/academics/syllabus/', include('syllabus.urls')),
    # Analytics and dashboard
    path('api/analytics/', include(('academics.api.urls', 'analytics'), namespace='analytics')),
    path('api/dashboard/', include(('academics.api.urls', 'dashboard'), namespace='dashboard')),

    # New app URLs
    path('api/library/', include('library.urls')),
    path('api/inventory/', include('inventory.urls')),
    path('api/communication/', include('communication.urls')),
    path('api/settings/', include('settings_app.urls')),
    path('api/fleet/', include('fleet.urls')),
    path('api/community/', include('community.urls')),
    path('api/mpesa/', include('mpesa_integration.urls')),
    path('api/statistics/', include('school_statistics.urls')),

    # API Documentation
    path('swagger<format>/', schema_view.without_ui(cache_timeout=0), name='schema-json'),
    path('swagger/', schema_view.with_ui('swagger', cache_timeout=0), name='schema-swagger-ui'),
    path('redoc/', schema_view.with_ui('redoc', cache_timeout=0), name='schema-redoc'),
] + static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)

if settings.DEBUG:
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
